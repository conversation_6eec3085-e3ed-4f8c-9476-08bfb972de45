import os
import requests
from io import BytesIO
import fitz 
import docx
from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardButton, InlineKeyboardMarkup, CallbackQuery

api_id = 29108471
api_hash = "3c084bd30d228e5f40e98f298f3130a2"
bot_token = "8110283500:AAEOy6Z8lD4BCRycOgWQ38lrCWcRTPZh_EM"
app = Client("Sozdar_bot", api_id=api_id, api_hash=api_hash, bot_token=bot_token)

Translate_Options = InlineKeyboardMarkup([
    [InlineKeyboardButton("🇸🇦 ترجمة من العربية إلى الإنجليزية", callback_data="ar_to_en")],
    [InlineKeyboardButton("🇺🇸 ترجمة من الإنجليزية إلى العربية", callback_data="en_to_ar")],
])

user_translate_direction = {}

def translate_text(text, source_lang, target_lang):
    url = "https://translate.googleapis.com/translate_a/single"
    params = {
        "client": "gtx",
        "sl": source_lang,
        "tl": target_lang,
        "dt": "t",
        "q": text,
    }
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            return "".join([i[0] for i in data[0]])
        else:
            return "❌ خطأ في الترجمة من الخادم"
    except Exception as e:
        return f"❌ فشل الاتصال: {e}"

def extract_text_from_pdf(file_path):
    try:
        text = ""
        with fitz.open(file_path) as doc:
            for page in doc:
                text += page.get_text()
        return text.strip()
    except Exception:
        return None

def extract_text_from_docx(file_path):
    try:
        doc = docx.Document(file_path)
        return "\n".join([para.text for para in doc.paragraphs]).strip()
    except Exception:
        return None

@app.on_message(filters.command("start") & filters.private)
async def start_handler(client: Client, message: Message):
    await message.reply_text(
        f"يا مَرحَباً بِكَ يا : {message.from_user.username} ✨\n"
        "لُطفاً إِختَر إتجاه الترجمة 🖤",
        reply_markup=Translate_Options
    )

@app.on_callback_query()
async def callback_handler(client: Client, callback_query: CallbackQuery):
    user_id = callback_query.from_user.id
    if callback_query.data == "ar_to_en":
        user_translate_direction[user_id] = ("ar", "en")
    elif callback_query.data == "en_to_ar":
        user_translate_direction[user_id] = ("en", "ar")

    await callback_query.message.delete()
    await callback_query.message.reply("لُطفاً أرسِل مَلفكَ (PDF او WORD) ✨")
    await callback_query.answer()

@app.on_message(filters.document & filters.private)
async def document_handler(client: Client, message: Message):
    user_id = message.from_user.id

    if user_id not in user_translate_direction:
        await message.reply("❗ يرجى اختيار اتجاه الترجمة أولاً باستخدام /start")
        return

    file_ext = os.path.splitext(message.document.file_name)[1].lower()
    if file_ext not in [".pdf", ".docx"]:
        await message.reply("⚠️ الملف غير مدعوم. الرجاء إرسال ملف PDF أو Word فقط.")
        return

    loading_msg = await message.reply(" جاري تحميل الملف...")
    file_path = await message.download()

    await loading_msg.edit("📄 جاري استخراج النص من الملف...")
    if file_ext == ".pdf":
        extracted_text = extract_text_from_pdf(file_path)
    else:
        extracted_text = extract_text_from_docx(file_path)

    if not extracted_text:
        await loading_msg.edit("❌ فشل في استخراج النص من الملف.")
        return

    await loading_msg.edit(" جاري الترجمة، يرجى الانتظار...")

    source_lang, target_lang = user_translate_direction[user_id]
    chunks = [extracted_text[i:i+4000] for i in range(0, len(extracted_text), 4000)]
    translated_result = ""
    for chunk in chunks:
        translated_result += translate_text(chunk, source_lang, target_lang) + "\n"

    with BytesIO() as f:
        f.write(translated_result.encode("utf-8"))
        f.name = "translated.txt"
        f.seek(0)
        await message.reply_document(f, caption="ما أَمَرتَ بهِ قَد تَم 🤍")

    await loading_msg.delete()

    await message.reply_text(
        "✨ هل ترغب بخدمة أخرى؟ اختر اتجاه الترجمة:",
        reply_markup=Translate_Options
    )

    os.remove(file_path)

app.run()
