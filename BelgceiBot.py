import io
import os
from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardButton, InlineKeyboardMarkup
import pdfplumber
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from PIL import Image


api_id = 29108471
api_hash = "3c084bd30d228e5f40e98f298f3130a2"
bot_token = "8174262200:AAG2HCNylHOgjTq3jfEdKARR8wTjwK_fDb0"


app = Client("belgeci_bot", api_id=api_id, api_hash=api_hash, bot_token=bot_token)
user_status = {}
pdfmetrics.registerFont(TTFont("Noto", os.path.join(os.path.dirname(__file__), "NotoSans-Regular.ttf")))


Select_markup = InlineKeyboardMarkup([
    [InlineKeyboardButton("تَحويل نَص إِلى pdf ", callback_data="text")],
    [InlineKeyboardButton("تَحويل pdf إِلى نَص", callback_data="pdf")],
    [InlineKeyboardButton("تَحويل صورة إِلى pdf ", callback_data="image")]
])


@app.on_message(filters.command("start") & filters.private)
async def start_handler(client: Client, message: Message):
    await message.reply_text(
        f' حَللِتَ اهلاً طيباً يا :  {message.from_user.username}  لُطفَا إِختر ما جِئتَ لِأَجلِه 🤍 ',
        reply_markup=Select_markup
    )


@app.on_callback_query(filters.regex("text"))
async def handle_text_selection(client, callback_query):
    user_id = callback_query.from_user.id
    user_status[user_id] = "awaiting_text"
    await callback_query.message.reply_text("لُطفاً أرسِل نَصكَ وَ سَوفَ يَتِم تَحويلة 🤍")
    await callback_query.answer()


def generate_pdf(text):
    buffer = io.BytesIO()
    c = canvas.Canvas(buffer, pagesize=A4)
    c.setFont("Noto", 14)

    width, height = A4
    x = 40
    y = height - 40

    for line in text.split("\n"):
        if y < 40:
            c.showPage()
            c.setFont("Noto", 14)
            y = height - 40
        c.drawString(x, y, line)
        y -= 20

    c.save()
    buffer.seek(0)
    return buffer


@app.on_message(filters.text & filters.private)
async def handle_text_to_pdf(client, message: Message):
    user_id = message.from_user.id

    if user_status.get(user_id) == "awaiting_text":
        text = message.text
        pdf_buffer = generate_pdf(text)

        await message.reply_document(
            document=pdf_buffer,
            file_name="converted_text.pdf",
            caption="أمرُكَ قَد تَم 🖤"
        )
        await message.reply_text(" إِن أَرَدْتَ خِدمَةً أُخرى 🖤", reply_markup=Select_markup)


        user_status.pop(user_id, None)


@app.on_callback_query(filters.regex("pdf")) 
async def handle_pdf_selection(client, callback_query):
    user_id = callback_query.from_user.id
    user_status[user_id] = "awaiting_pdf"
    await callback_query.message.reply_text("لُطفاً أرسِل مِلف PDF وَ سَوفَ أَستخرِج مِنه النَّص 🤍")
    await callback_query.answer()


@app.on_message(filters.document & filters.private)
async def handle_pdf_to_text(client, message: Message):
    user_id = message.from_user.id

    if user_status.get(user_id) == "awaiting_pdf":
        file_path = await message.download()
        
        try:
            with pdfplumber.open(file_path) as pdf:
                full_text = ""
                for page in pdf.pages:
                    full_text += page.extract_text() or ""
                    full_text += "\n" + ("=" * 50) + "\n"  

            if not full_text.strip():
                await message.reply_text("عُذرًا، لم أستطع استخراج أي نَص من هذا الملف 🤍")
            elif len(full_text) < 4000:
                await message.reply_text(f"ها هو النَّص المستخرج من الملف:\n\n{full_text}")
            else:
                txt_bytes = io.BytesIO(full_text.encode('utf-8'))
                txt_bytes.name = "extracted_text.txt"
                await message.reply_document(txt_bytes, caption="هذا هو النَّص المستخرج من ملف PDF 🖤")
            await message.reply_text(" إِن أَرَدْتَ خِدمَةً أُخرى 🖤", reply_markup=Select_markup)
            os.remove(file_path)



        except Exception as e:
            await message.reply_text(f"حدث خطأ أثناء معالجة الملف: {e}")
        
        user_status.pop(user_id, None)


@app.on_callback_query(filters.regex("image")) 
async def handle_image_selection(client, callback_query):
    user_id = callback_query.from_user.id
    user_status[user_id] = "awaiting_image"
    await callback_query.message.reply_text("أرسِل صُورة (PNG أو JPG) لِتحويلها إِلى PDF 🤍")
    await callback_query.answer()


@app.on_message(filters.photo & filters.private)
async def handle_image_to_pdf(client, message: Message):
    user_id = message.from_user.id

    if user_status.get(user_id) == "awaiting_image":
        try:
            file_path = await message.download()

            image = Image.open(file_path).convert("RGB")

            pdf_bytes = io.BytesIO()
            image.save(pdf_bytes, format="PDF")
            pdf_bytes.name = "converted_image.pdf"
            pdf_bytes.seek(0)

            await message.reply_document(pdf_bytes, caption="الصُّورة تَحوّلت إلى PDF بنجاح 🖤")
            await message.reply_text(" إِن أَرَدْتَ خِدمَةً أُخرى 🖤", reply_markup=Select_markup)
            os.remove(file_path)


        except Exception as e:
            await message.reply_text(f"حدث خَطأ أثناء التحويل: {e}")

        user_status.pop(user_id, None)


app.run()
