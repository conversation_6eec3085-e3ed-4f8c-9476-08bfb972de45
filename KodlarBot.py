from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from pyrogram.errors import FloodWait, MessageNotModified
import google.generativeai as genai
import asyncio
import logging

api_id = 29108471
api_hash = "3c084bd30d228e5f40e98f298f3130a2"
BOT_TOKEN = "7301398385:AAG7bUSIM0EpW8xEw3nked55Sf3s19xZjT4"
GOOGLE_API_KEY = "AIzaSyBq89pWqdkfhUpqKldFO_8-m3Xt_0M82ww"

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

genai.configure(api_key=GOOGLE_API_KEY)
app = Client("kodlar_bot", api_id=api_id, api_hash=api_hash, bot_token=BOT_TOKEN)

user_code = {}

# دالة لتنظيف البيانات القديمة
async def cleanup_old_data():
    """تنظيف البيانات القديمة للمستخدمين غير النشطين"""
    while True:
        try:
            await asyncio.sleep(3600)  # تنظيف كل ساعة
            current_time = asyncio.get_event_loop().time()
            to_remove = []

            for user_id, data in user_code.items():
                if 'timestamp' in data:
                    if current_time - data['timestamp'] > 3600:  # أكثر من ساعة
                        to_remove.append(user_id)

            for user_id in to_remove:
                user_code.pop(user_id, None)
                logger.info(f"Cleaned up old data for user {user_id}")

        except Exception as e:
            logger.error(f"Error in cleanup: {e}")

buttons = [
    [InlineKeyboardButton("🔁 تحويل كود", callback_data="translate_code")],
    [InlineKeyboardButton("🛠 تصحيح أخطاء", callback_data="fix_code")]
]

languages = [
    ["Python", "JavaScript"],
    ["C++", "Java"],
    ["C#", "Go"],
]

def get_language_buttons():
    keyboard = []
    for row in languages:
        keyboard.append([InlineKeyboardButton(lang, callback_data=f"lang_{lang}") for lang in row])
    return InlineKeyboardMarkup(keyboard)

@app.on_message(filters.command("start"))
async def start(client, message: Message):
    try:
        user_id = message.from_user.id
        user_code.pop(user_id, None)
        logger.info(f"User {user_id} started the bot")

        await message.reply(
            f"مرحباً بِكَ يا {message.from_user.username}⚡️ \n لطفاً اخبرني بما تُريد 🖤",
            reply_markup=InlineKeyboardMarkup(buttons)
        )
    except Exception as e:
        logger.error(f"Error in start command: {e}")
        await message.reply("❌ حدث خطأ، يرجى المحاولة مرة أخرى.")

@app.on_callback_query(filters.regex("translate_code"))
async def translate_code(client, callback_query: CallbackQuery):
    try:
        user_id = callback_query.from_user.id
        user_code[user_id] = {
            "step": "awaiting_code",
            "mode": "translate",
            "timestamp": asyncio.get_event_loop().time()
        }
        logger.info(f"User {user_id} selected translate code")

        await callback_query.message.edit_text("لُطفاً ارفق لي الكود المراد تحويله ✨")
        await callback_query.answer()
    except MessageNotModified:
        await callback_query.answer()
    except Exception as e:
        logger.error(f"Error in translate_code: {e}")
        await callback_query.answer("❌ حدث خطأ، يرجى المحاولة مرة أخرى.")

@app.on_callback_query(filters.regex("fix_code"))
async def fix_code(client, callback_query: CallbackQuery):
    try:
        user_id = callback_query.from_user.id
        user_code[user_id] = {
            "step": "awaiting_code",
            "mode": "fix",
            "timestamp": asyncio.get_event_loop().time()
        }
        logger.info(f"User {user_id} selected fix code")

        await callback_query.message.edit_text("✍️ أرسل الكود الذي تريد تصحيحه:")
        await callback_query.answer()
    except MessageNotModified:
        await callback_query.answer()
    except Exception as e:
        logger.error(f"Error in fix_code: {e}")
        await callback_query.answer("❌ حدث خطأ، يرجى المحاولة مرة أخرى.")

@app.on_callback_query(filters.regex("lang_"))
async def handle_language_choice(client, callback_query: CallbackQuery):
    try:
        user_id = callback_query.from_user.id
        if user_id not in user_code or user_code[user_id].get("mode") != "translate":
            await callback_query.answer("لا توجد عملية تحويل جارية.")
            return

        lang = callback_query.data.split("_")[1]
        user_code[user_id]["language"] = lang
        logger.info(f"User {user_id} selected language: {lang}")

        await callback_query.answer()
        await callback_query.message.delete()
        await process_code_translation(callback_query.message, user_id)
    except Exception as e:
        logger.error(f"Error in handle_language_choice: {e}")
        await callback_query.answer("❌ حدث خطأ، يرجى المحاولة مرة أخرى.")

@app.on_message(filters.text & ~filters.command(["start"]))
async def receive_code(client, message: Message):
    try:
        user_id = message.from_user.id

        if user_id not in user_code:
            await message.reply("❗️يرجى اختيار وظيفة أولاً باستخدام /start.")
            return

        if user_code[user_id]["step"] == "awaiting_code":
            user_code[user_id]["code"] = message.text
            logger.info(f"User {user_id} sent code for {user_code[user_id]['mode']}")

            if user_code[user_id]["mode"] == "translate":
                user_code[user_id]["step"] = "awaiting_language"
                await message.reply("🌐 اختر اللغة التي تريد التحويل إليها:", reply_markup=get_language_buttons())
            elif user_code[user_id]["mode"] == "fix":
                await process_code_fix(message, user_id)
    except Exception as e:
        logger.error(f"Error in receive_code: {e}")
        await message.reply("❌ حدث خطأ، يرجى المحاولة مرة أخرى.")

async def process_code_translation(message: Message, user_id):
    try:
        code = user_code[user_id].get("code")
        target_lang = user_code[user_id].get("language")

        loading_msg = await message.reply("🔄 جاري تحويل الكود...")
        logger.info(f"Processing code translation for user {user_id} to {target_lang}")

        prompt = f"حوّل الكود التالي إلى لغة {target_lang}. أرسل الكود المحول فقط بدون شرح:\n\n{code}"

        try:
            model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.3,
                    max_output_tokens=2000
                )
            )

            if response and response.text:
                result = response.text.strip()
                logger.info(f"Successfully translated code for user {user_id}")
            else:
                result = "❌ لم أتمكن من تحويل الكود."
                logger.warning(f"Empty response for user {user_id}")

        except Exception as e:
            result = f"❌ حدث خطأ أثناء تحويل الكود:\n{str(e)}"
            logger.error(f"AI error for user {user_id}: {e}")

        try:
            await loading_msg.delete()
        except:
            pass

        await message.reply(f"📤 الكود بعد التحويل:\n\n```{target_lang.lower()}\n{result}\n```")
        await message.reply("إِن كانَ لكَ طَلبُ آخر 🤍", reply_markup=InlineKeyboardMarkup(buttons))

    except Exception as e:
        logger.error(f"Error in process_code_translation: {e}")
        try:
            await loading_msg.delete()
        except:
            pass
        await message.reply("❌ حدث خطأ أثناء معالجة الكود.")
    finally:
        user_code.pop(user_id, None)


async def process_code_fix(message: Message, user_id):
    try:
        code = user_code[user_id].get("code")

        loading_msg = await message.reply("🛠 جاري تصحيح الكود...")
        logger.info(f"Processing code fix for user {user_id}")

        prompt = f"راجع وصحح الكود التالي واشرح الأخطاء الموجودة:\n\n{code}"

        try:
            model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.5,
                    max_output_tokens=2000
                )
            )

            if response and response.text:
                result = response.text.strip()
                logger.info(f"Successfully fixed code for user {user_id}")
            else:
                result = "❌ لم أتمكن من تحليل الكود أو إيجاد الأخطاء."
                logger.warning(f"Empty response for code fix user {user_id}")

        except Exception as e:
            result = f"❌ حدث خطأ أثناء مراجعة الكود:\n{str(e)}"
            logger.error(f"AI error for code fix user {user_id}: {e}")

        try:
            await loading_msg.delete()
        except:
            pass

        await message.reply(f"🔍 النتيجة:\n\n{result}")
        await message.reply("إِن كانَ لكَ طَلبُ آخر 🤍", reply_markup=InlineKeyboardMarkup(buttons))

    except Exception as e:
        logger.error(f"Error in process_code_fix: {e}")
        try:
            await loading_msg.delete()
        except:
            pass
        await message.reply("❌ حدث خطأ أثناء معالجة الكود.")
    finally:
        user_code.pop(user_id, None)

if __name__ == "__main__":
    try:
        logger.info("Starting KodlarBot...")

        # تشغيل دالة التنظيف في الخلفية
        loop = asyncio.get_event_loop()
        loop.create_task(cleanup_old_data())

        app.run()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"❌ خطأ في تشغيل البوت: {e}")
    finally:
        logger.info("Bot shutdown complete")

