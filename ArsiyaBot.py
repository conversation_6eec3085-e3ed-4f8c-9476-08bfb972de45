from pyrogram import Client, filters
from pyrogram.types import ReplyKeyboardMarkup

api_id = 29108471
api_hash = "3c084bd30d228e5f40e98f298f3130a2"
bot_token = "7602916553:AAFbnyTjuBrDWdfWvJgTol7YwDKyFL5F-iQ"

app = Client("ArşiyaBot", api_id=api_id, api_hash=api_hash, bot_token=bot_token)

Main = ReplyKeyboardMarkup(
    keyboard=[["وَاجِبّات📚"], ["مُلَخَّصات📓"]],
    resize_keyboard=True
)

Stages = ReplyKeyboardMarkup(
    keyboard=[
        ["مَرّحَلة أولىَ 1️⃣"],
        ["مَرّحَلة ثانية 2️⃣"],
        ["مَرّحَلة ثَالِثة 3️⃣"],
        ["مَرّحَلة رَابِعة 4️⃣"],
        ["عَودَة 🔙"]
    ],
    resize_keyboard=True
)

First_stage = ReplyKeyboardMarkup(
    keyboard=[
       ["Programming Fundamental 1"],
       ["Programming Fundamental 2"],
       ["Computer Organisation"],
       ["Computer Skills"],
       ["Digital Logic"],
       ["Discrete Structure"],
       ["رياضيات 1"],
       ["رياضيات 2"],
       ["حقوق الإنسان"],
       ["أخلاقيات المهنة"],
       ["انكليزي 1"],
       ["عربي 1"],
        ["عَودَة 🔙"]
    ],
    resize_keyboard=True
)

Sec_Stage =  ReplyKeyboardMarkup(
    keyboard=[
       ["OOP"],
       ["Database"],
       ["Data structure"],
       ["Computing Theory"],
       ["Probability and Statistics"],
       ["Artificial intelligence 1"],
       ["Data management"],
       ["Algorithms"],
       ["Software 1"],
       ["Micro processor"],
       ["انكليزي 2"],
       ["عربي 2"],
       ["جرائم حزب البعث"],
        ["عَودَة 🔙"]
    ],
    resize_keyboard=True
)

@app.on_message(filters.command("start"))
async def Start_bot(client, message):
    await message.reply_text(f"أهلاً بِكَ {message.from_user.username} 🤍", reply_markup=Main)

@app.on_message(filters.text & filters.regex(r"^مُلَخَّصات📓$"))
async def OP_1(client, message):
    await message.reply_text(f"لُطفاً إِختَر مَرحَلة {message.from_user.username} 🖤", reply_markup=Stages)

@app.on_message(filters.text & filters.regex(r"^وَاجِبّات📚$"))
async def OP_2(client, message):
    await message.reply_text(f"لُطفاً إِختَر مَرحَلة {message.from_user.username} 🖤", reply_markup=Stages)

@app.on_message(filters.text & filters.regex(r"^عَودَة 🔙$"))
async def OP_Back(client, message):
    await message.reply_text(f"أختَر جَنِباً يا  {message.from_user.username} 🖤", reply_markup = Main)







app.run()